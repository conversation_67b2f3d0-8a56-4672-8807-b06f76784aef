import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Search, Sparkles, X } from "lucide-react";
import { useCampaignParams } from "@/utils/urlParams";

interface NewProjectOptionsModalProps {
  onUploadFile: () => void;
  onBrowseStock: () => void;
  onGenerateAI: () => void;
}

export const NewProjectOptionsModal = ({
  onUploadFile,
  onBrowseStock,
  onGenerateAI,
}: NewProjectOptionsModalProps) => {
  const campaignParams = useCampaignParams();
  
  // Modal is open when the URL parameter is set
  const isOpen = campaignParams.modal === 'new-project-options';

  const handleClose = () => {
    campaignParams.clearModal();
  };

  const handleUploadFile = () => {
    handleClose();
    onUploadFile();
  };

  const handleBrowseStock = () => {
    handleClose();
    onBrowseStock();
  };

  const handleGenerateAI = () => {
    handleClose();
    onGenerateAI();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Create new project</DialogTitle>
          <DialogDescription>
            Choose how you'd like to start your new image editing project
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <h3 className="text-lg font-medium">General</h3>
          
          <div className="grid gap-4">
            {/* Upload File Option */}
            <Card 
              className="cursor-pointer hover:bg-accent/50 transition-colors border-2 hover:border-primary/20"
              onClick={handleUploadFile}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center">
                    <Upload className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-base font-medium">Upload a file</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Import your image file to start editing with text overlays and effects
                    </CardDescription>
                  </div>
                  <div className="w-4 h-4 rounded-full border-2 border-muted-foreground/30" />
                </div>
              </CardHeader>
            </Card>

            {/* Browse Stock Option */}
            <Card 
              className="cursor-pointer hover:bg-accent/50 transition-colors border-2 hover:border-primary/20"
              onClick={handleBrowseStock}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-lg bg-green-50 flex items-center justify-center">
                    <Search className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-base font-medium">Browse Stock</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Choose from thousands of high-quality stock images from Pixabay and Unsplash
                    </CardDescription>
                  </div>
                  <div className="w-4 h-4 rounded-full border-2 border-muted-foreground/30" />
                </div>
              </CardHeader>
            </Card>

            {/* Generate AI Option */}
            <Card 
              className="cursor-pointer hover:bg-accent/50 transition-colors border-2 hover:border-primary/20"
              onClick={handleGenerateAI}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-lg bg-purple-50 flex items-center justify-center">
                    <Sparkles className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-base font-medium">Generate AI</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Create unique images using AI image generation powered by DALL-E
                    </CardDescription>
                  </div>
                  <div className="w-4 h-4 rounded-full border-2 border-muted-foreground/30" />
                </div>
              </CardHeader>
            </Card>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
