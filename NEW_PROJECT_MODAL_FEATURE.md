# New Project Options Modal Feature

## Overview
This feature adds a popup modal that appears when users navigate to a new project (`mode=new`) without specifying a project title. The modal provides three options for starting a new image editing project, similar to the design shown in the reference image.

## Implementation Details

### URL Parameter Integration
- **Trigger**: <PERSON><PERSON> appears when URL has `mode=new` but no `projectTitle` parameter
- **Modal State**: Controlled via URL parameter `modal=new-project-options`
- **Example URLs**:
  - `http://localhost:8081/campaign?mode=new` → Shows modal
  - `http://localhost:8081/campaign?mode=new&projectTitle=MyProject` → No modal (direct to editor)

### Modal Options
The modal presents three options with appropriate icons and descriptions:

1. **Upload a file** 📁
   - Icon: Upload icon (blue theme)
   - Description: "Import your image file to start editing with text overlays and effects"
   - Action: Opens file picker for image upload

2. **Browse Stock** 🔍
   - Icon: Search icon (green theme)
   - Description: "Choose from thousands of high-quality stock images from Pixabay and Unsplash"
   - Action: Opens stock browser modal (`modal=stock-browser`)

3. **Generate AI** ✨
   - Icon: Sparkles icon (purple theme)
   - Description: "Create unique images using AI image generation powered by DALL-E"
   - Action: Opens AI generator modal (`modal=ai-generator`)

### Files Modified

#### 1. `src/utils/urlParams.ts`
- Added `'new-project-options'` to the modal type union
- Updated all type definitions and validation functions

#### 2. `src/components/NewProjectOptionsModal.tsx` (New File)
- React component implementing the modal UI
- Uses URL parameters for state management
- Handles all three option actions
- Follows existing modal patterns in the codebase

#### 3. `src/pages/ImageGenerator.tsx`
- Added import for `NewProjectOptionsModal`
- Added useEffect to show modal when `mode=new` and no `projectTitle`
- Added handler functions for the three modal options:
  - `handleUploadFile()`: Creates file input and processes upload
  - `handleBrowseStock()`: Sets modal to 'stock-browser'
  - `handleGenerateAI()`: Sets modal to 'ai-generator'
- Rendered modal component in JSX

### Design Consistency
The modal follows the same design patterns as existing modals in the application:
- Uses shadcn/ui Dialog components
- Consistent with existing modal state management via URL parameters
- Matches the visual style of other option selection modals
- Responsive design with proper spacing and hover effects

### User Experience Flow
1. User navigates to `/campaign?mode=new` (without projectTitle)
2. Modal automatically appears with three options
3. User selects an option:
   - **Upload**: File picker opens, image uploads, modal closes
   - **Browse Stock**: Modal closes, stock browser opens
   - **Generate AI**: Modal closes, AI generator opens
4. User can also click "Cancel" to close modal without action

### Testing
To test the feature:
1. Navigate to `http://localhost:8081/campaign?mode=new`
2. Verify modal appears with three options
3. Test each option works correctly
4. Verify modal doesn't appear when projectTitle is provided
5. Verify modal doesn't appear in existing mode

### Integration Notes
- The feature integrates seamlessly with existing URL parameter management
- No breaking changes to existing functionality
- Follows established patterns for modal state management
- Compatible with existing stock browser and AI generator features
