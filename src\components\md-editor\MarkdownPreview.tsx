import { useEffect, useRef } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import mermaid from "mermaid";


interface MarkdownPreviewProps {
  markdown: string;
}

// Custom Mermaid component
const MermaidDiagram = ({ children }: { children: string }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const renderDiagram = async () => {
        try {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'inherit',
            fontSize: 14,
          });

          const { svg } = await mermaid.render(`mermaid-${Date.now()}`, children);
          if (ref.current) {
            ref.current.innerHTML = svg;
          }
        } catch (error) {
          console.error('Mermaid rendering error:', error);
          if (ref.current) {
            ref.current.innerHTML = `<p class="text-destructive text-sm">Error rendering diagram: ${error}</p>`;
          }
        }
      };

      renderDiagram();
    }
  }, [children]);

  return <div ref={ref} className="my-4 flex justify-center" />;
};

export const MarkdownPreview = ({ markdown }: MarkdownPreviewProps) => {
  return (
    <div className="w-full h-full bg-preview border rounded-lg overflow-auto">
      <div id="markdown-content" className="p-6 prose prose-sm max-w-none dark:prose-invert">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
          components={{
            code({ className, children, ...props }: any) {
              const match = /language-(\w+)/.exec(className || '');
              const language = match ? match[1] : '';
              const isInline = !className;
              
              if (!isInline && language === 'mermaid') {
                return <MermaidDiagram>{String(children).replace(/\n$/, '')}</MermaidDiagram>;
              }
              
              return (
                <code 
                  className={`${className || ''} ${!isInline ? 'block bg-code p-3 rounded-md text-sm' : 'bg-code px-1 py-0.5 rounded text-sm'}`} 
                  {...props}
                >
                  {children}
                </code>
              );
            },
            h1: ({ children }) => (
              <h1 className="text-2xl font-bold mb-4 pb-2 border-b">{children}</h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-xl font-semibold mb-3 mt-6">{children}</h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-lg font-medium mb-2 mt-4">{children}</h3>
            ),
            p: ({ children }) => (
              <p className="mb-4 leading-relaxed">{children}</p>
            ),
            ul: ({ children }) => (
              <ul className="mb-4 pl-6 space-y-1">{children}</ul>
            ),
            ol: ({ children }) => (
              <ol className="mb-4 pl-6 space-y-1">{children}</ol>
            ),
            li: ({ children }) => (
              <li className="leading-relaxed">{children}</li>
            ),
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-primary/30 pl-4 my-4 italic text-muted-foreground">
                {children}
              </blockquote>
            ),
            table: ({ children }) => (
              <div className="overflow-x-auto my-4">
                <table className="min-w-full border border-border rounded-lg">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="border border-border bg-muted px-4 py-2 text-left font-medium">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-border px-4 py-2">{children}</td>
            ),
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>
    </div>
  );
};