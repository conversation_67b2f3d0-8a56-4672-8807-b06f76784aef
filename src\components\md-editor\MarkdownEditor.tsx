import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileDown, Eye, Edit } from "lucide-react";
import { MarkdownPreview } from "./MarkdownPreview";

import { toast } from "sonner";

const defaultMarkdown = `# Markdown Preview with Mermaid

This is a **markdown editor** that supports *Mermaid diagrams*.

## Features

- Real-time markdown preview
- Mermaid diagram rendering
- PDF export functionality
- Beautiful syntax highlighting

## Sample Diagram

\`\`\`mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[End]
\`\`\`

## Code Example

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

## Lists

1. First item
2. Second item
   - Nested item
   - Another nested item

## Table

| Feature | Supported |
|---------|-----------|
| Markdown | ✅ |
| Mermaid | ✅ |
| PDF Export | ✅ |
`;

export const MarkdownEditor = () => {
  const [markdown, setMarkdown] = useState(defaultMarkdown);
  const [activeTab, setActiveTab] = useState<"editor" | "preview">("editor");


  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-xl font-semibold bg-gradient-primary bg-clip-text text-transparent">
                Markdown Preview
              </h1>
              <div className="hidden md:flex items-center gap-2 text-sm text-muted-foreground">
                <span>•</span>
                <span>With Mermaid Support</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Mobile tab switcher */}
              <div className="md:hidden flex items-center bg-muted rounded-lg p-1">
                <Button
                  variant={activeTab === "editor" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("editor")}
                  className="h-8 px-3"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant={activeTab === "preview" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("preview")}
                  className="h-8 px-3"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
              
           
                <Button size="sm" className="shadow-elegant">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>
           
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto p-4">
        <div className="grid md:grid-cols-2 gap-4 h-[calc(100vh-120px)]">
          {/* Editor Panel */}
          <div className={`${activeTab === "preview" ? "hidden md:block" : ""} flex flex-col`}>
            <div className="mb-3 flex items-center gap-2">
              <h2 className="text-sm font-medium text-muted-foreground">Editor</h2>
              <div className="h-px bg-border flex-1" />
            </div>
            <div className="flex-1 relative">
              <textarea
                value={markdown}
                onChange={(e) => setMarkdown(e.target.value)}
                className="w-full h-full p-4 text-sm font-mono bg-editor border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                placeholder="Enter your markdown here..."
                spellCheck={false}
              />
            </div>
          </div>

          {/* Preview Panel */}
          <div className={`${activeTab === "editor" ? "hidden md:block" : ""} flex flex-col`}>
            <div className="mb-3 flex items-center gap-2">
              <h2 className="text-sm font-medium text-muted-foreground">Preview</h2>
              <div className="h-px bg-border flex-1" />
            </div>
            <div className="flex-1 overflow-hidden">
              <MarkdownPreview markdown={markdown} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};